/**
 * 蓝牙工具使用演示页面
 * 展示如何使用封装的蓝牙工具类
 */

import SimpleBluetooth from '../../utils/SimpleBluetooth'

Page({
  data: {
    // 界面状态
    isScanning: false,
    isBroadcasting: false,
    isInitialized: false,
    
    // 配置参数
    payload: '1234567890abcdef',
    filterName: 'TEMP',
    
    // 数据列表
    receiveDataList: [],
    
    // 统计信息
    totalReceived: 0,
    lastReceiveTime: ''
  },

  /**
   * 页面加载
   */
  async onLoad() {
    console.log('蓝牙演示页面加载')
    await this.initBluetooth()
  },

  /**
   * 页面卸载
   */
  onUnload() {
    console.log('蓝牙演示页面卸载')
    SimpleBluetooth.close()
  },

  /**
   * 初始化蓝牙
   */
  async initBluetooth() {
    try {
      wx.showLoading({ title: '初始化蓝牙...' })
      
      await SimpleBluetooth.init({
        enableAdvertise: true
      })
      
      this.setData({ isInitialized: true })
      
      wx.hideLoading()
      wx.showToast({
        title: '蓝牙初始化成功',
        icon: 'success'
      })
      
      console.log('蓝牙初始化成功')
    } catch (error) {
      wx.hideLoading()
      wx.showModal({
        title: '初始化失败',
        content: `蓝牙初始化失败: ${error.message}`,
        showCancel: false
      })
      console.error('蓝牙初始化失败:', error)
    }
  },

  /**
   * 开始接收数据
   */
  async startReceive() {
    if (!this.data.isInitialized) {
      wx.showToast({
        title: '请先初始化蓝牙',
        icon: 'none'
      })
      return
    }

    try {
      await SimpleBluetooth.startReceive(this.data.filterName, (data) => {
        this.onDataReceived(data)
      })
      
      this.setData({ isScanning: true })
      
      wx.showToast({
        title: '开始接收数据',
        icon: 'success'
      })
      
      console.log('开始接收数据')
    } catch (error) {
      wx.showToast({
        title: '开始接收失败',
        icon: 'none'
      })
      console.error('开始接收失败:', error)
    }
  },

  /**
   * 停止接收数据
   */
  stopReceive() {
    SimpleBluetooth.stopReceive()
    this.setData({ isScanning: false })
    
    wx.showToast({
      title: '停止接收',
      icon: 'success'
    })
    
    console.log('停止接收数据')
  },

  /**
   * 开始发送广播
   */
  async startBroadcast() {
    if (!this.data.isInitialized) {
      wx.showToast({
        title: '请先初始化蓝牙',
        icon: 'none'
      })
      return
    }

    const payload = this.data.payload.replace(/\s+/g, '')
    
    if (!payload || payload.length === 0) {
      wx.showToast({
        title: '请输入要发送的数据',
        icon: 'none'
      })
      return
    }

    try {
      await SimpleBluetooth.send(payload)
      
      this.setData({ isBroadcasting: true })
      
      wx.showToast({
        title: '开始发送广播',
        icon: 'success'
      })
      
      console.log('开始发送广播:', payload)
    } catch (error) {
      wx.showToast({
        title: '发送失败',
        icon: 'none'
      })
      console.error('发送广播失败:', error)
    }
  },

  /**
   * 停止发送广播
   */
  async stopBroadcast() {
    try {
      await SimpleBluetooth.stopSend()
      
      this.setData({ isBroadcasting: false })
      
      wx.showToast({
        title: '停止发送',
        icon: 'success'
      })
      
      console.log('停止发送广播')
    } catch (error) {
      console.error('停止发送失败:', error)
    }
  },

  /**
   * 数据接收回调
   */
  onDataReceived(data) {
    console.log('接收到数据:', data)
    
    const receiveDataList = this.data.receiveDataList
    receiveDataList.unshift(data)
    
    // 限制列表长度
    if (receiveDataList.length > 50) {
      receiveDataList.splice(50)
    }
    
    this.setData({
      receiveDataList,
      totalReceived: this.data.totalReceived + 1,
      lastReceiveTime: data.time
    })
  },

  /**
   * 清空接收数据
   */
  clearReceiveData() {
    this.setData({
      receiveDataList: [],
      totalReceived: 0,
      lastReceiveTime: ''
    })
    
    wx.showToast({
      title: '数据已清空',
      icon: 'success'
    })
  },

  /**
   * 复制数据
   */
  copyData() {
    if (this.data.receiveDataList.length === 0) {
      wx.showToast({
        title: '没有数据可复制',
        icon: 'none'
      })
      return
    }
    
    const data = this.data.receiveDataList
      .map(item => `${item.time}: ${item.data}`)
      .join('\n')
    
    wx.setClipboardData({
      data,
      success: () => {
        wx.showToast({
          title: '复制成功',
          icon: 'success'
        })
      }
    })
  },

  /**
   * payload输入变化
   */
  onPayloadInput(e) {
    this.setData({
      payload: e.detail.value
    })
  },

  /**
   * 过滤名称输入变化
   */
  onFilterNameInput(e) {
    this.setData({
      filterName: e.detail.value
    })
  },

  /**
   * 重新初始化蓝牙
   */
  async reinitBluetooth() {
    // 先关闭现有连接
    SimpleBluetooth.close()
    
    this.setData({
      isInitialized: false,
      isScanning: false,
      isBroadcasting: false
    })
    
    // 重新初始化
    await this.initBluetooth()
  }
})
