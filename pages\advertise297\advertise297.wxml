<!--index.wxml-->
<view class="page-body">
	<view class="page-section">
		<!-- 🎯 设备详细信息显示区域 -->
		<view class="device-info-section">
			<view>设备名称: {{filterName || '0000'}}</view>
			<view>系统类型: {{system || 'unknown'}}</view>
			<view>广播状态: {{advertiseStart ? '正在发送' : '未发送'}}</view>
			<view>接收状态: {{discoveryStarted ? '正在接收' : '未接收'}}</view>
			<view>广播就绪: {{advertiseReady ? '是' : '否'}}</view>
			<view>接收就绪: {{discoveryReady ? '是' : '否'}}</view>
			<view>发现设备数: {{devices.length || 0}}</view>
			<view>接收数据条数: {{receiveDataList.length || 0}}</view>
		</view>

		<view class="cus_tips">用户可以根据特定设备，手动发送自定义的数据</view>

		<!-- 🎯 预设指令下拉框 -->
		<view class="command-selector">
			<view class="selector-label">预设指令:</view>
			<picker bindchange="onCommandSelect" value="{{commandIndex}}" range="{{commandList}}" range-key="name">
				<view class="picker-display">
					{{commandList[commandIndex].name || '选择预设指令'}}
				</view>
			</picker>
		</view>

		<view class="address-wrapper">
			<!-- <view>payload</view> -->

			<input class="address-wrapper-input" value="{{payload}}" type="text" name="payload" placeholder="请填写payload" bindinput="onPayloadChange" />
			<!-- <image src="/images/icon_setting.png" class="icon-setting"></image> -->
			<button bindtap="gotoConfig" size="mini" plain="true" type="warn" class="btn-scan btn-scan-left"> 设置</button>
		</view>





		<view class="scroll-view_H" scroll-x="true" enable-flex="true" bindscroll="scroll">
			<view class="top-btn-group">
				<button wx-if="{{!advertiseStart}}" size="mini" bindtap="startSendAndDiscovery" type="primary" plain="true">发送广播包</button>
				<button wx:else bindtap="stopAdvertising" size="mini" type="warn" plain="true">停止发送</button>
				<button  bindtap="clickCopy" size="mini" type="primary" plain="true">Copy数据</button>
				<button  bindtap="printBluetoothDevices" size="mini" type="default" plain="true">打印设备</button>
			</view>

		</view>
		<view>
				<view class="viewDivider"></view>
		</view>
		<view class="scanResultGroup">
			<view class="result-content" wx:for="{{receiveDataList}}">
				{{item.time}}：{{item.data}}
			</view>
		</view>


		<view class="scroll-view_V" scroll-y="true" bindscroll="scroll" style="width: 100%">

			<view class="filter_form">
				<input value="{{filterName}}" class="localNameInput" bindinput="onLocalNameChange" placeholder="请输入过滤的localName"></input>
				<view class="top-btn-group">
					<button wx-if="{{!discoveryStarted}}" size="large" style="width:200rpx;" class="receive_btn" bindtap="startDiscovery" type="primary">接收</button>
					<button wx:else bindtap="stopBluetoothDevicesDiscovery" style="width: 200rpx;" class="receive_btn" size="large" type="warn">停止</button>
				</view>
				<button size="large" bindtap="clickClearResult" type="primary" style="width: 200rpx;" class="receive_btn">清空</button>
				<button size="large" bindtap="getBluetoothAdapterState" type="default" style="width: 200rpx;" class="receive_btn">蓝牙状态</button>
			</view>


		</view>

	</view>

</view>