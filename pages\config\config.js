//index.js
//获取应用实例
const app = getApp()
Page({
  data: {
    channel: '37',
    address: ''
  },
  //事件处理函数
  formSubmit: function (e) {
    const data = e.detail.value;
    const address = data.address
    const channel = data.channel
    wx.setStorageSync('address', address)
    wx.setStorageSync('channel', channel)
    wx.navigateBack({
      delta: 1
    })
  },
  formReset(e) {
    this.setData({
      channel: '37',
      address: '',
      payload: ''
    })
  },
  onLoad: function () {
    const channel = wx.getStorageSync('channel') || '37'
    const address = wx.getStorageSync('address')

    // 🎯 打印当前配置的地址
    console.log('=== 当前配置信息 ===')
    console.log('📡 信道 (channel):', channel)
    console.log('📍 地址 (address):', address || '未设置')
    console.log('📍 地址长度:', address ? address.length : 0, '字符')
    if (address) {
      console.log('📍 地址格式:', address.replace(/\s+/g, ''))
      console.log('📍 字节数:', address.replace(/\s+/g, '').length / 2)
    }
    console.log('==================')

    this.setData({
      channel, address
    })

  }
})
