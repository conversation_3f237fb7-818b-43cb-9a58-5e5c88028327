/**
 * 简化版蓝牙工具类
 * 提供最常用的蓝牙功能，简化使用方式
 */

import { ab2hex } from './util'
import { generateData, getServiceUUIDs } from './BLEUtil'

class SimpleBluetooth {
  constructor() {
    this.isInitialized = false
    this.server = null
    this.system = 'android'
    this.callbacks = {}
    
    this.initSystemInfo()
  }

  initSystemInfo() {
    try {
      const { system } = wx.getSystemInfoSync()
      this.system = system
    } catch (error) {
      console.error('获取系统信息失败:', error)
    }
  }

  /**
   * 初始化蓝牙
   * @param {Object} options - 配置选项
   * @returns {Promise}
   */
  async init(options = {}) {
    if (this.isInitialized) {
      return Promise.resolve()
    }

    try {
      // 初始化蓝牙适配器
      await this.openBluetoothAdapter()
      
      // 如果需要广播功能，创建服务器
      if (options.enableAdvertise !== false) {
        await this.createServer()
      }
      
      this.isInitialized = true
      console.log('蓝牙初始化成功')
      return Promise.resolve()
    } catch (error) {
      console.error('蓝牙初始化失败:', error)
      throw error
    }
  }

  /**
   * 打开蓝牙适配器
   */
  openBluetoothAdapter() {
    return new Promise((resolve, reject) => {
      wx.openBluetoothAdapter({
        mode: 'peripheral',
        success: resolve,
        fail: reject
      })
    })
  }

  /**
   * 创建BLE服务器
   */
  createServer() {
    return new Promise((resolve, reject) => {
      wx.createBLEPeripheralServer()
        .then(res => {
          this.server = res.server
          resolve(res)
        })
        .catch(reject)
    })
  }

  /**
   * 开始扫描蓝牙设备
   * @param {Object} options - 扫描选项
   * @param {string} options.filterName - 过滤设备名称
   * @param {function} options.onDeviceFound - 发现设备回调
   * @param {function} options.onDataReceived - 接收数据回调
   */
  async startScan(options = {}) {
    const { filterName, onDeviceFound, onDataReceived } = options

    try {
      // 开始扫描
      await new Promise((resolve, reject) => {
        wx.startBluetoothDevicesDiscovery({
          allowDuplicatesKey: true,
          powerLevel: 'high',
          success: resolve,
          fail: reject
        })
      })

      // 监听设备发现
      wx.onBluetoothDeviceFound((res) => {
        res.devices.forEach(device => {
          // 过滤设备名称
          if (filterName && device.localName !== filterName) {
            return
          }

          // 处理广播数据
          if (device.advertisData) {
            let hexData = ab2hex(device.advertisData)
            hexData = hexData.substring(4)
            device.data = hexData

            // 触发回调
            if (onDeviceFound) onDeviceFound(device)
            if (onDataReceived) {
              const receiveData = {
                time: new Date().toLocaleTimeString(),
                data: hexData,
                device
              }
              onDataReceived(receiveData)
            }
          }
        })
      })

      console.log('开始扫描蓝牙设备')
    } catch (error) {
      console.error('开始扫描失败:', error)
      throw error
    }
  }

  /**
   * 停止扫描
   */
  stopScan() {
    wx.offBluetoothDeviceFound()
    console.log('停止扫描蓝牙设备')
  }

  /**
   * 发送广播数据
   * @param {string} payload - 要发送的数据
   */
  async sendBroadcast(payload) {
    if (!this.server) {
      throw new Error('蓝牙服务器未初始化')
    }

    if (!payload || payload.length === 0) {
      throw new Error('payload不能为空')
    }

    try {
      // 处理数据
      const cleanPayload = payload.replace(/\s+/g, '')
      const isIos = this.system.indexOf('iOS') >= 0
      const isIos13 = isIos && this.system.indexOf('13.') >= 0
      
      const actPayload = generateData(cleanPayload, isIos)
      if (!actPayload) {
        throw new Error('生成广播数据失败')
      }

      const uuids = getServiceUUIDs(actPayload, isIos13)

      // 开始广播
      await this.server.startAdvertising({
        advertiseRequest: {
          connectable: true,
          deviceName: isIos ? '11' : '',
          serviceUuids: isIos ? uuids : [],
          manufacturerData: isIos ? [] : [{
            manufacturerId: '0x00C7',
            manufacturerSpecificData: actPayload,
          }]
        },
        powerLevel: 'high'
      })

      console.log('开始广播数据:', payload)
    } catch (error) {
      console.error('发送广播失败:', error)
      throw error
    }
  }

  /**
   * 停止广播
   */
  async stopBroadcast() {
    if (this.server) {
      this.server.stopAdvertising()
      console.log('停止广播')
    }
  }

  /**
   * 关闭蓝牙
   */
  close() {
    this.stopScan()
    this.stopBroadcast()
    
    if (this.server) {
      this.server.close()
      this.server = null
    }
    
    wx.closeBluetoothAdapter()
    this.isInitialized = false
    console.log('关闭蓝牙')
  }
}

// 创建单例实例
const simpleBluetooth = new SimpleBluetooth()

// 导出便捷方法
export default {
  /**
   * 初始化蓝牙
   */
  init: (options) => simpleBluetooth.init(options),

  /**
   * 开始扫描并接收数据
   * @param {string} filterName - 过滤设备名称
   * @param {function} onDataReceived - 数据接收回调
   */
  startReceive: (filterName, onDataReceived) => {
    return simpleBluetooth.startScan({
      filterName,
      onDataReceived
    })
  },

  /**
   * 发送广播数据
   * @param {string} payload - 要发送的数据
   */
  send: (payload) => simpleBluetooth.sendBroadcast(payload),

  /**
   * 停止接收
   */
  stopReceive: () => simpleBluetooth.stopScan(),

  /**
   * 停止发送
   */
  stopSend: () => simpleBluetooth.stopBroadcast(),

  /**
   * 关闭蓝牙
   */
  close: () => simpleBluetooth.close(),

  /**
   * 获取原始实例（用于高级功能）
   */
  getInstance: () => simpleBluetooth
}
