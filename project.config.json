{"description": "项目配置文件", "packOptions": {"ignore": [], "include": []}, "setting": {"urlCheck": true, "es6": true, "postcss": true, "preloadBackgroundData": false, "minified": true, "newFeature": true, "coverView": true, "autoAudits": false, "showShadowRootInWxmlPanel": true, "scopeDataCheck": false, "uglifyFileName": true, "checkInvalidKey": true, "checkSiteMap": true, "uploadWithSourceMap": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "useCompilerModule": false, "userConfirmedUseCompilerModuleSwitch": false, "enhance": true, "compileWorklet": false, "packNpmManually": false, "packNpmRelationList": [], "minifyWXSS": true, "minifyWXML": true, "localPlugins": false, "disableUseStrict": false, "useCompilerPlugins": false, "condition": false, "swc": false, "disableSWC": true}, "compileType": "miniprogram", "libVersion": "3.8.9", "appid": "wx8ed8d9627230acb4", "projectname": "panBT", "simulatorType": "wechat", "simulatorPluginLibVersion": {}, "condition": {}, "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}}