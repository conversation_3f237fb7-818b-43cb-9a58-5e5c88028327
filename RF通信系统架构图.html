<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RF通信系统架构图 - 全屏查看</title>
    <script src="https://unpkg.com/mermaid@10/dist/mermaid.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            padding: 15px 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }

        .title {
            color: #333;
            font-size: 24px;
            font-weight: bold;
            text-align: center;
            margin-bottom: 15px;
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 10px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            color: white;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }

        .btn-primary {
            background: linear-gradient(45deg, #007bff, #0056b3);
        }

        .btn-success {
            background: linear-gradient(45deg, #28a745, #1e7e34);
        }

        .btn-warning {
            background: linear-gradient(45deg, #ffc107, #e0a800);
        }

        .btn-danger {
            background: linear-gradient(45deg, #dc3545, #c82333);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .diagram-container {
            flex: 1;
            padding: 20px;
            display: flex;
            justify-content: center;
            align-items: center;
            overflow: hidden;
        }

        .diagram-wrapper {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            padding: 20px;
            max-width: 95vw;
            max-height: 80vh;
            overflow: auto;
            transition: transform 0.3s ease;
        }

        .mermaid {
            text-align: center;
            min-height: 400px;
        }

        .shortcuts {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 15px;
            border-radius: 10px;
            font-size: 12px;
            opacity: 0.7;
            transition: opacity 0.3s ease;
        }

        .shortcuts:hover {
            opacity: 1;
        }

        .shortcuts h4 {
            margin-bottom: 8px;
            color: #ffc107;
        }

        .shortcuts div {
            margin: 3px 0;
        }

        @media (max-width: 768px) {
            .controls {
                gap: 5px;
            }

            .btn {
                padding: 8px 15px;
                font-size: 12px;
            }

            .title {
                font-size: 20px;
            }
        }
    </style>
</head>

<body>
    <div class="header">
        <h1 class="title">🔌 RF通信系统架构图</h1>
        <div class="controls">
            <button class="btn btn-primary" onclick="zoomIn()">🔍 放大</button>
            <button class="btn btn-primary" onclick="zoomOut()">🔍 缩小</button>
            <button class="btn btn-success" onclick="resetZoom()">🔄 重置</button>
            <button class="btn btn-warning" onclick="toggleFullscreen()">📺 全屏</button>
            <button class="btn btn-danger" onclick="downloadSVG()">💾 下载</button>
        </div>
    </div>

    <div class="diagram-container">
        <div class="diagram-wrapper" id="diagramWrapper">
            <div class="mermaid" id="diagram">
                graph TB
                subgraph "🖥️ 用户界面层"
                UI1["📝 Payload输入框<br />示例: 01 02 03 04"]
                UI2["🚀 发送按钮<br />发送广播包"]
                UI3["📡 接收按钮<br />开始接收"]
                UI4["⚙️ 设置按钮<br />配置参数"]
                UI5["📺 数据显示区<br />接收数据列表"]
                end

                subgraph "📋 配置数据层"
                CONFIG1["📍 RF设备地址<br />AA BB CC DD EE<br />存储: wx.storage"]
                CONFIG2["📻 通信信道<br />Channel: 37/38/39<br />存储: wx.storage"]
                CONFIG3["🏷️ 设备过滤名<br />FilterName: TEMP<br />存储: 内存"]
                CONFIG4["📱 系统类型<br />iOS/Android<br />检测: wx.getSystemInfoSync"]
                end

                subgraph "🔵 蓝牙适配器层"
                BLE1["📡 广播适配器<br />Peripheral Mode<br />状态: advertiseReady"]
                BLE2["🔍 扫描适配器<br />Central Mode<br />状态: discoveryReady"]
                BLE3["🖥️ BLE服务器<br />createBLEPeripheralServer<br />管理: servers数组"]
                end

                subgraph "🔧 数据处理层"
                PROCESS1["✅ 数据验证<br />格式检查<br />长度验证"]
                PROCESS2["🔄 地址处理<br />反序排列<br />EE DD CC BB AA"]
                PROCESS3["📦 载荷处理<br />十六进制转换<br />字节数组"]
                PROCESS4["🛡️ CRC计算<br />check_crc16<br />2字节校验"]
                end

                subgraph "🎭 协议转换层"
                PROTO1["🔀 XN297L处理<br />位反转 + 白化<br />种子: 0x3F"]
                PROTO2["🎨 BLE处理<br />BLE白化<br />种子: Channel"]
                PROTO3["📱 平台适配<br />iOS: ServiceUUIDs<br />Android: ManufacturerData"]
                end

                subgraph "📡 传输层"
                TRANS1["📻 BLE广播<br />startAdvertising<br />功率: high"]
                TRANS2["🔍 BLE扫描<br />startBluetoothDevicesDiscovery<br />重复: true"]
                TRANS3["🏷️ 设备过滤<br />localName匹配<br />提取: advertisData"]
                end

                subgraph "🔌 RF设备层"
                RF1["📟 XN297L芯片<br />2.4GHz RF<br />接收BLE数据"]
                RF2["📤 RF设备响应<br />发送数据<br />通过BLE广播"]
                end

                subgraph "🔄 数据流向说明"
                FLOW1["➡️ 发送流程<br />用户数据 → BLE → RF设备"]
                FLOW2["⬅️ 接收流程<br />RF设备 → BLE → 用户界面"]
                FLOW3["⚙️ 配置流程<br />用户设置 → 本地存储 → 系统使用"]
                end

                %% 连接关系
                UI1 --> PROCESS1
                UI2 --> PROCESS1
                UI4 --> CONFIG1
                UI4 --> CONFIG2

                CONFIG1 --> PROCESS2
                CONFIG2 --> PROTO2
                CONFIG3 --> TRANS3
                CONFIG4 --> PROTO3

                PROCESS1 --> PROCESS3
                PROCESS2 --> PROTO1
                PROCESS3 --> PROTO1
                PROCESS4 --> PROTO1

                PROTO1 --> PROTO2
                PROTO2 --> PROTO3
                PROTO3 --> TRANS1

                BLE1 --> TRANS1
                BLE2 --> TRANS2
                BLE3 --> TRANS1

                TRANS1 --> RF1
                RF2 --> TRANS2
                TRANS2 --> TRANS3
                TRANS3 --> UI5

                %% 样式定义
                classDef userLayer fill:#e3f2fd,stroke:#1976d2,stroke-width:3px,color:#000
                classDef configLayer fill:#f3e5f5,stroke:#7b1fa2,stroke-width:3px,color:#000
                classDef bleLayer fill:#e8f5e8,stroke:#388e3c,stroke-width:3px,color:#000
                classDef processLayer fill:#fff3e0,stroke:#f57c00,stroke-width:3px,color:#000
                classDef protoLayer fill:#fce4ec,stroke:#c2185b,stroke-width:3px,color:#000
                classDef transLayer fill:#e0f2f1,stroke:#00695c,stroke-width:3px,color:#000
                classDef rfLayer fill:#fff8e1,stroke:#f9a825,stroke-width:3px,color:#000
                classDef flowLayer fill:#f1f8e9,stroke:#558b2f,stroke-width:3px,color:#000

                class UI1,UI2,UI3,UI4,UI5 userLayer
                class CONFIG1,CONFIG2,CONFIG3,CONFIG4 configLayer
                class BLE1,BLE2,BLE3 bleLayer
                class PROCESS1,PROCESS2,PROCESS3,PROCESS4 processLayer
                class PROTO1,PROTO2,PROTO3 protoLayer
                class TRANS1,TRANS2,TRANS3 transLayer
                class RF1,RF2 rfLayer
                class FLOW1,FLOW2,FLOW3 flowLayer
            </div>
        </div>
    </div>

    <div class="shortcuts">
        <h4>⌨️ 快捷键</h4>
        <div><kbd>+</kbd> / <kbd>=</kbd> 放大</div>
        <div><kbd>-</kbd> 缩小</div>
        <div><kbd>0</kbd> 重置</div>
        <div><kbd>F11</kbd> 全屏</div>
        <div><kbd>S</kbd> 下载</div>
    </div>

    <script>
        // 初始化 Mermaid
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: false,
                htmlLabels: true,
                curve: 'basis'
            },
            themeVariables: {
                fontSize: '14px',
                fontFamily: 'Microsoft YaHei, Arial, sans-serif'
            }
        });

        let currentZoom = 1;
        const diagramWrapper = document.getElementById('diagramWrapper');

        function zoomIn() {
            currentZoom = Math.min(currentZoom + 0.1, 3);
            updateZoom();
        }

        function zoomOut() {
            currentZoom = Math.max(currentZoom - 0.1, 0.3);
            updateZoom();
        }

        function resetZoom() {
            currentZoom = 1;
            updateZoom();
        }

        function updateZoom() {
            diagramWrapper.style.transform = `scale(${currentZoom})`;
            diagramWrapper.style.transformOrigin = 'center center';
        }

        function toggleFullscreen() {
            if (!document.fullscreenElement) {
                document.documentElement.requestFullscreen().catch(err => {
                    console.log('无法进入全屏模式:', err);
                });
            } else {
                document.exitFullscreen();
            }
        }

        function downloadSVG() {
            const svg = document.querySelector('.mermaid svg');
            if (svg) {
                const svgData = new XMLSerializer().serializeToString(svg);
                const svgBlob = new Blob([svgData], { type: 'image/svg+xml;charset=utf-8' });
                const svgUrl = URL.createObjectURL(svgBlob);
                const downloadLink = document.createElement('a');
                downloadLink.href = svgUrl;
                downloadLink.download = 'RF通信系统架构图.svg';
                document.body.appendChild(downloadLink);
                downloadLink.click();
                document.body.removeChild(downloadLink);
                URL.revokeObjectURL(svgUrl);
            }
        }

        // 键盘快捷键
        document.addEventListener('keydown', function (e) {
            switch (e.key) {
                case '+':
                case '=':
                    e.preventDefault();
                    zoomIn();
                    break;
                case '-':
                    e.preventDefault();
                    zoomOut();
                    break;
                case '0':
                    e.preventDefault();
                    resetZoom();
                    break;
                case 'F11':
                    e.preventDefault();
                    toggleFullscreen();
                    break;
                case 's':
                case 'S':
                    if (e.ctrlKey || e.metaKey) {
                        e.preventDefault();
                        downloadSVG();
                    }
                    break;
            }
        });

        // 鼠标滚轮缩放
        diagramWrapper.addEventListener('wheel', function (e) {
            if (e.ctrlKey) {
                e.preventDefault();
                if (e.deltaY < 0) {
                    zoomIn();
                } else {
                    zoomOut();
                }
            }
        });

        // 页面加载完成后的提示
        window.addEventListener('load', function () {
            setTimeout(() => {
                console.log('🎉 RF通信系统架构图已加载完成！');
                console.log('💡 使用快捷键或按钮来操作图表');
            }, 1000);
        });
    </script>
</body>

</html>