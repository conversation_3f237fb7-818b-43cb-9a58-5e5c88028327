/**
 * BluetoothManager 使用示例
 * 展示如何在页面中使用封装的蓝牙管理工具
 */

import BluetoothManager from './BluetoothManager'

// 示例页面代码
Page({
  data: {
    devices: [],
    receiveDataList: [],
    advertiseStart: false,
    discoveryStarted: false,
    payload: '',
    filterName: 'TEMP'
  },

  onLoad() {
    this.initBluetoothManager()
  },

  onUnload() {
    // 页面卸载时清理蓝牙资源
    if (this.bluetoothManager) {
      this.bluetoothManager.destroy()
    }
  },

  /**
   * 初始化蓝牙管理器
   */
  initBluetoothManager() {
    // 创建蓝牙管理器实例
    this.bluetoothManager = new BluetoothManager({
      filterName: this.data.filterName,
      maxReceiveDataCount: 100,
      powerLevel: 'high'
    })

    // 设置事件监听
    this.setupBluetoothEvents()

    // 初始化蓝牙适配器
    this.initBluetoothAdapters()
  },

  /**
   * 设置蓝牙事件监听
   */
  setupBluetoothEvents() {
    // 监听设备发现
    this.bluetoothManager.on('onDeviceFound', (device) => {
      console.log('发现设备:', device)
      this.setData({
        devices: this.bluetoothManager.getDevices()
      })
    })

    // 监听数据接收
    this.bluetoothManager.on('onDataReceived', (data) => {
      console.log('接收到数据:', data)
      this.setData({
        receiveDataList: this.bluetoothManager.getReceiveDataList()
      })
    })

    // 监听广播状态变化
    this.bluetoothManager.on('onAdvertiseStateChange', (state) => {
      console.log('广播状态变化:', state)
      this.setData({
        advertiseStart: state.started
      })
    })

    // 监听发现状态变化
    this.bluetoothManager.on('onDiscoveryStateChange', (state) => {
      console.log('发现状态变化:', state)
      this.setData({
        discoveryStarted: state.started
      })
    })

    // 监听错误
    this.bluetoothManager.on('onError', (error) => {
      console.error('蓝牙错误:', error)
      wx.showToast({
        title: error.message || '蓝牙操作失败',
        icon: 'none'
      })
    })
  },

  /**
   * 初始化蓝牙适配器
   */
  async initBluetoothAdapters() {
    try {
      // 同时初始化发现和广播适配器
      await Promise.all([
        this.bluetoothManager.initDiscoveryAdapter(),
        this.bluetoothManager.initAdvertiseAdapter()
      ])
      console.log('蓝牙适配器初始化成功')
    } catch (error) {
      console.error('蓝牙适配器初始化失败:', error)
      wx.showModal({
        title: '提示',
        content: '蓝牙初始化失败，请检查蓝牙权限',
        showCancel: false
      })
    }
  },

  /**
   * 开始发送广播
   */
  async startSendAndDiscovery() {
    const payload = this.data.payload.replace(/\s+/g, '')
    
    if (!payload || payload.length === 0) {
      wx.showModal({
        title: '提示',
        content: '请填写payload',
        showCancel: false
      })
      return
    }

    try {
      await this.bluetoothManager.startAdvertising(payload)
      console.log('开始广播成功')
    } catch (error) {
      console.error('开始广播失败:', error)
    }
  },

  /**
   * 停止广播
   */
  async stopAdvertising() {
    try {
      await this.bluetoothManager.stopAdvertising()
      console.log('停止广播成功')
    } catch (error) {
      console.error('停止广播失败:', error)
    }
  },

  /**
   * 开始设备发现
   */
  async startDiscovery() {
    try {
      await this.bluetoothManager.startBluetoothDevicesDiscovery()
      console.log('开始设备发现成功')
    } catch (error) {
      console.error('开始设备发现失败:', error)
    }
  },

  /**
   * 停止设备发现
   */
  stopDiscovery() {
    this.bluetoothManager.stopBluetoothDevicesDiscovery()
    console.log('停止设备发现')
  },

  /**
   * 清空接收数据
   */
  clearReceiveData() {
    this.bluetoothManager.clearReceiveDataList()
    this.setData({
      receiveDataList: []
    })
  },

  /**
   * 复制数据到剪贴板
   */
  copyData() {
    const data = this.data.receiveDataList
      .map(item => `${item.time}：${item.data}`)
      .join('\n')
    
    wx.setClipboardData({
      data,
      success: () => {
        wx.showToast({
          title: '复制成功！'
        })
      }
    })
  },

  /**
   * payload输入变化
   */
  onPayloadChange(e) {
    this.setData({
      payload: e.detail.value
    })
  },

  /**
   * 过滤名称变化
   */
  onFilterNameChange(e) {
    const filterName = e.detail.value
    this.setData({
      filterName
    })
    // 更新蓝牙管理器的过滤名称
    this.bluetoothManager.setFilterName(filterName)
  }
})

// 导出蓝牙管理器类供其他文件使用
export { BluetoothManager }
