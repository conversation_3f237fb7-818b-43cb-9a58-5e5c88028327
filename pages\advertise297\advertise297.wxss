/**index.wxss**/
.page-body {
  height: 100%;
  background-color: white;
}

/* 🎯 设备名字显示样式 */
.device-name-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20rpx 30rpx;
  margin: 20rpx 0;
  border-radius: 15rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 4rpx 20rpx rgba(102, 126, 234, 0.3);
}

.device-name-label {
  color: rgba(255, 255, 255, 0.8);
  font-size: 28rpx;
  margin-right: 15rpx;
}

.device-name-value {
  color: #ffffff;
  font-size: 32rpx;
  font-weight: bold;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}

.btn-scan {
  width: 140rpx;
}

.address-wrapper {
  margin-left: 20rpx;
  display: flex;
  flex-direction: row;
  align-items: center;
}

.address-wrapper-input {
  height: 60rpx;
  padding: 15rpx;
  margin: 15rpx;
  flex: 1;
  font-size: 30rpx;
  border-radius: 15rpx;
  border: #999999 1rpx solid;
}

.cus_tips {
  font-size: 24rpx;
  margin-left: 30rpx;
  margin-top: 30rpx;
}

.icon-setting {
  width: 50rpx;
  height: 50rpx;
}

.btn-scan-left {
  margin-right: 20rpx;
}

.top-btn-group {
  flex-direction: row;
  display: flex;
  margin-right: 15rpx;

}

.result-title {
  margin-top: 20rpx;
  margin-bottom: 20rpx;
  margin-left: 40rpx;
}

.receive_btn {}

.result-content {
  margin-top: 20rpx;
  white-space: normal;
  word-break: break-all;
  padding-right: 10rpx;
}

.page-section {
  height: 100%;
  padding-left: 20rpx;
  padding-right: 20rpx;
  display: flex;
  flex-direction: column;
}

.scroll-view_V {
  justify-content: start;
  margin-bottom: 30rpx;
}

.ble-item {
  width: 100%;
  height: 120rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding-left: 30rpx;
}

.divLine {
  background: #999999;
  width: 100%;
  height: 2rpx;
}

.ble-item-name {}

.viewDivider {
  height: 2rpx;
  background-color: #a1a1a1;
  margin-top: 30rpx;
  margin-bottom: 20rpx;
}

.scanResultGroup {
  flex-grow: 1;
  overflow-y: scroll;
  padding-right: 25rpx;
}

.filter_form {
  display: flex;
  flex-direction: row;
  align-items: center;

}

.localNameInput {
  height: 60rpx;
  padding: 15rpx;
  margin: 15rpx;
  flex: 1;
  font-size: 30rpx;
  border-radius: 15rpx;
  border: #999999 1rpx solid;
}

/* 🎯 预设指令下拉框样式 */
.command-selector {
  display: flex;
  align-items: center;
  margin: 20rpx 0;
  padding: 0 20rpx;
}

.selector-label {
  font-size: 28rpx;
  color: #333;
  margin-right: 20rpx;
  min-width: 120rpx;
}

.picker-display {
  flex: 1;
  height: 60rpx;
  line-height: 60rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333;
  background-color: #f8f8f8;
  border: 1rpx solid #ddd;
  border-radius: 10rpx;
  text-align: left;
}