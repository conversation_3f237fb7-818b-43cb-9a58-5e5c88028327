/**
 * RF通信测试工具 - 主页面逻辑
 * 功能：通过BLE广播实现与XN297L RF设备的通信
 * 作者：RF通信团队
 * 更新：2024年
 */

//获取应用实例
const app = getApp()

// 导入工具函数
import { ab2hex, inArray } from "../../utils/util"           // 数据转换和数组操作工具
import { generateData, getServiceUUIDs } from "../../utils/BLEUtil"  // BLE数据处理工具

// 默认设备过滤名称，用于接收时过滤特定设备
const filterName = 'TEMP'

Page({
  // 系统类型，默认为android，会在初始化时检测并更新
  system: 'android',

  /**
   * 页面数据定义
   */
  data: {
    // === 蓝牙状态控制 ===
    discoveryStarted: false,    // 是否已开始设备发现（扫描）
    advertiseStart: false,      // 是否已开始广播
    discoveryReady: false,      // 发现适配器是否就绪
    advertiseReady: false,      // 广播适配器是否就绪

    // === 用户输入数据 ===
    payload: '',                // 用户输入的载荷数据（十六进制）

    // === 设备管理 ===
    devices: [],                // 发现的蓝牙设备列表
    servers: [],                // BLE服务器实例数组
    serverId: '',               // 当前服务器ID

    // === 接收数据管理 ===
    filterName: filterName,     // 设备名称过滤器
    receiveDataList: [],        // 接收到的数据列表 [{time: '时间', data: '数据'}]
    // receiveDataList示例: [{time:'上午:10:20',data:'4367462347234798623473632343637236437234686437'}]

    // === 系统信息 ===
    system: 'unknown',          // 系统类型（iOS/Android）

    // === 预设指令 ===
    commandIndex: 0,            // 当前选中的指令索引
    commandList: [              // 预设指令列表
      { name: '请选择指令', command: '' },
      { name: '前进', command: '81000210011300FA' },
      { name: '后退', command: '81000210021400FA' },
      { name: '刹车', command: '81000210031500FA' },
      { name: '转停', command: '81000214001600FA' },
      { name: '左转', command: '81000214011700FA' },
      { name: '右转', command: '81000214021800FA' },
      { name: '50% 车速', command: '81000212324600FA' },
      { name: '30% 车速', command: '810002121E3200FA' },
      { name: '开启驻车', command: '8100021A011D00FA' },
      { name: '关闭驻车', command: '8100021A001C00FA' }
    ]
  },

  /**
   * 跳转到配置页面
   * 用于设置RF设备地址、通信信道等参数
   */
  gotoConfig() {
    wx.navigateTo({
      url: '/pages/config/config',
    })
  },

  /**
   * 页面加载时的初始化函数
   * 按顺序初始化各个组件
   */
  onLoad() {
    this.initSystemInfo()        // 1. 获取系统信息（iOS/Android）
    this.initPayload();          // 2. 初始化载荷数据（从本地存储读取）
    this.initAdvertiseAdapter()  // 3. 初始化广播适配器（发送功能）
    this.initDiscoveryAdapter()  // 4. 初始化发现适配器（查找设备功能）
  },

  /**
   * 页面卸载时的清理函数
   * 停止所有蓝牙活动，释放资源
   */
  onUnload() {
    this.stopAdvertising()                    // 停止BLE广播
    this.stopBluetoothDevicesDiscovery()      // 停止设备扫描
    this.data.servers.forEach(server => {    // 关闭所有BLE服务器
      server.close()
    })

    // 🎯 清理响应检测
    if (this.responseTimeout) {
      clearTimeout(this.responseTimeout)
      this.waitingForResponse = false
    }
  },

  /**
   * 清空接收数据列表
   * 用户点击"清空"按钮时调用
   */
  clickClearResult() {
    this.setData({ receiveDataList: [] })
  },

  /**
   * 复制接收到的数据到剪贴板
   * 将所有接收数据格式化为文本并复制
   */
  clickCopy() {
    // 将接收数据数组转换为文本格式：时间：数据
    const data = this.data.receiveDataList.map(item => item.time + "：" + item.data).join("\n")

    wx.setClipboardData({
      data,
      success: function (res) {
        wx.showToast({
          title: '复制成功！',
        })
        // 可选：读取剪贴板验证
        // wx.getClipboardData({
        //   success:function (res) {
        //     console.log(res.data)// data
        //   }
        // })
      }
    })
  },

  /**
   * 打印当前配置的地址信息
   */
  printCurrentConfig() {
    const address = wx.getStorageSync('address')
    const channel = wx.getStorageSync('channel')

    console.log('=== 当前RF配置信息 ===')
    console.log('📡 信道:', channel || '未设置')
    console.log('📍 原始地址:', address || '未设置')

    if (address) {
      const cleanAddress = address.replace(/\s+/g, '').toLowerCase()
      console.log('📍 清理后地址:', cleanAddress)
      console.log('📍 地址长度:', cleanAddress.length, '字符')
      console.log('📍 字节数:', cleanAddress.length / 2)
      console.log('📍 地址字节:', cleanAddress.match(/.{2}/g) || [])
    } else {
      console.log('⚠️ 地址未配置，请在设置页面配置RF设备地址')
    }
    console.log('========================')
  },

  /**
   * 调试函数：打印当前连接的蓝牙设备
   * 用于开发调试，显示所有已发现的设备信息
   */
  printBluetoothDevices() {
    console.log('=== 当前连接的蓝牙设备汇总 ===')

    if (this.data.devices.length === 0) {
      console.log('❌ 没有发现任何蓝牙设备')
      return
    }

    console.log(`📊 总共发现 ${this.data.devices.length} 个设备`)
    console.log('📋 设备列表详情:')

    this.data.devices.forEach((device, index) => {
      console.log(`\n--- 设备 ${index + 1} ---`)
      console.log('📱 设备名称:', device.localName || device.name || '未知')
      console.log('🆔 设备ID:', device.deviceId || '无ID')
      console.log('🔋 信号强度:', device.RSSI || '未知')
      console.log('📦 最新数据:', device.data || '无数据')
      console.log('⏰ 最后更新:', new Date().toLocaleString())

      if (device.serviceUUIDs && device.serviceUUIDs.length > 0) {
        console.log('🏷️ 服务数量:', device.serviceUUIDs.length)
      }
    })

    console.log('\n=== 汇总打印完成 ===')
  },

  /**
   * 调试函数：获取并打印蓝牙适配器状态
   * 用于诊断蓝牙连接问题
   */
  getBluetoothAdapterState() {
    wx.getBluetoothAdapterState({
      success: (res) => {
        console.log('=== 蓝牙适配器状态 ===')
        console.log('蓝牙适配器是否可用:', res.available)
        console.log('蓝牙适配器是否正在搜索设备:', res.discovering)
        console.log('完整状态信息:', res)
      },
      fail: (res) => {
        console.log('获取蓝牙适配器状态失败:', res)
      }
    })
  },

  /**
   * 初始化系统信息
   * 检测当前运行平台（iOS/Android），影响BLE数据封装方式
   */
  initSystemInfo() {
    const { system } = wx.getSystemInfoSync();
    console.log('system', system)
    this.system = system  // 保存系统类型，用于后续的平台适配

    // 🎯 更新页面显示的系统信息
    this.setData({ system: system })
  },

  /**
   * 初始化载荷数据
   * 从本地存储读取上次输入的payload数据
   */
  initPayload() {
    wx.getStorage({
      key: 'payload',
      success: (res) => {
        if (res.data) {
          this.setData({ payload: res.data })  // 恢复上次的输入内容
        }
      }
    })
  },
  /**
   * 载荷输入框内容变化事件
   * 用户输入payload时实时更新数据
   */
  onPayloadChange(e) {
    const payload = e.detail.value
    // console.log('payload',payload)
    this.setData({ payload })  // 更新页面数据
  },

  /**
   * 处理预设指令选择
   * 用户选择下拉框中的指令时触发
   */
  onCommandSelect(e) {
    const index = e.detail.value
    const selectedCommand = this.data.commandList[index]

    console.log('🎯 === 选择预设指令 ===')
    console.log('📋 指令名称:', selectedCommand.name)
    console.log('📋 指令内容:', selectedCommand.command)
    console.log('=====================')

    this.setData({
      commandIndex: index,
      payload: selectedCommand.command  // 自动填入输入框
    })

    // 如果选择了有效指令，显示提示
    if (selectedCommand.command) {
      wx.showToast({
        title: `已选择: ${selectedCommand.name}`,
        icon: 'success',
        duration: 1500
      })
    }
  },

  /**
   * 设备名称过滤器输入变化事件
   * 用户修改接收设备过滤名称时调用
   */
  onLocalNameChange(e) {
    const localName = e.detail.value
    // console.log('localName',localName)
    this.setData({ filterName: localName })  // 更新过滤器名称
  },

  /**
   * 初始化设备发现适配器（接收功能） 查找设备和连接设备
   * 以中心设备模式打开蓝牙适配器，用于扫描和接收数据
   */
  initDiscoveryAdapter() {
    wx.openBluetoothAdapter({
      // mode: 'peripheral',  // 注释掉，使用默认的中心设备模式
      success: (res) => {
        console.log('initDiscoveryAdapter success', res)
        this.initBluetoothDevicesDiscovery();  // 初始化蓝牙成功后 初始化设备发现
      },
      fail: (res) => {
        console.log("initDiscoveryAdapter ble unavailable!", res)
        // 可选：显示错误提示
        // wx.showModal({
        //   content:'初始化失败',
        //   cancelColor: 'cancelColor',
        // })
      }
    })
  },

  /**
   * 初始化广播适配器（发送功能）
   * 以外设模式打开蓝牙适配器，用于发送BLE广播
   */
  initAdvertiseAdapter() {
    wx.openBluetoothAdapter({
      mode: 'peripheral',  // 外设模式，用于广播数据
      success: (res) => {
        console.log('initAdvertiseAdapter success', res)
        this.createBLEPeripheralServer()  // 成功后创建BLE外设服务器
      },
      fail: (res) => {
        console.log("initAdvertiseAdapter ble unavailable!", res)
        // 可选：显示错误提示
        // wx.showModal({
        //   content:'初始化失败',
        //   cancelColor: 'cancelColor',
        // })
      }
    })
  },

  /**
   * 开始发送数据和发现设备
   * 用户点击"发送广播包"按钮时调用  发送指令，按钮获取输入数据
   * 主要流程：验证数据 → 保存数据 → 检查状态 → 生成BLE数据 → 开始广播
   */
  startSendAndDiscovery() {
    // 1. 获取并清理payload数据（去除空格）
    let payload = this.data.payload
    payload = payload.replace(/\s+/g, '')

    // 2. 验证payload是否为空
    if (!payload || payload.length === 0) {
      wx.showModal({
        content: '请填写payload',
        cancelColor: 'cancelColor',
      })
      return
    }

    // 3. 保存payload到本地存储
    wx.setStorage({
      data: payload,
      key: 'payload',
    })

    // 4. 检查广播适配器是否就绪
    if (!this.data.advertiseReady) {
      wx.showModal({
        content: '初始化未成功',
        cancelColor: 'cancelColor',
      })
      console.log('发送初始化失败')
      this.initAdvertiseAdapter()  // 重新初始化
      return
    }

    // 5. 检测系统平台并生成BLE数据包
    const isIos = this.system.indexOf('iOS') >= 0
    const actPayload = generateData(payload, isIos);  // 调用BLEUtil生成最终数据包

    // 6. 开始BLE广播 需要到BLEUtil.js文件中的startAdvertising函数转换为小程序支持的格式
    this.startAdvertising(actPayload)

    // 🎯 记录发送时间和次数，用于分析连续发送
    if (!this.sendCount) this.sendCount = 0
    this.sendCount++
    this.lastSendTime = Date.now()

    console.log('📤 === 连续发送分析 ===')
    console.log('🔢 发送次数:', this.sendCount)
    console.log('⏰ 发送时间:', new Date().toLocaleTimeString())
    if (this.previousSendTime) {
      const interval = this.lastSendTime - this.previousSendTime
      // console.log('⏱️ 距离上次发送:', interval, 'ms')
      if (interval < 1000) {
        // console.log('⚠️ 连续发送间隔很短 (<1秒)')
      }
    }
    this.previousSendTime = this.lastSendTime
    console.log('========================')

    // 🎯 设置设备响应检测
    this.setupResponseDetection(payload)
  },

  /**
   * 设置设备响应检测
   * 发送指令后自动检测设备是否响应
   */
  setupResponseDetection(sentCommand) {
    console.log('📤 === 指令发送完成 ===')
    console.log('📋 发送的指令:', sentCommand)
    console.log('⏰ 发送时间:', new Date().toLocaleString())
    console.log('🔍 开始等待设备响应...')

    // 设置响应等待状态
    this.waitingForResponse = true
    this.sentCommand = sentCommand
    this.responseStartTime = Date.now()

    // 自动开启接收功能（如果未开启）
    if (!this.data.discoveryStarted) {
      console.log('🔄 自动开启接收功能，监听设备响应')
      setTimeout(() => {
        this.startDiscovery()
      }, 500)  // 延迟500ms后开始接收
    }

    // 设置响应超时检测（10秒）
    this.responseTimeout = setTimeout(() => {
      if (this.waitingForResponse) {
        const waitTime = ((Date.now() - this.responseStartTime) / 1000).toFixed(1)

        // 🎯 连续发送超时分析
        console.log('⏰ === 设备响应超时 ===')
        console.log('❌ 设备可能未接收到指令或无响应')
        console.log('� 发送次数:', this.sendCount || 1)
        // console.log('🔢 响应次数:', this.responseCount || 0)
        console.log('�📋 发送的指令:', this.sentCommand)
        // console.log('⏱️ 等待时间:', waitTime, '秒')

        if (this.sendCount > 1) {
          console.log('� 连续发送分析:')
          console.log('  - 可能设备正在处理前一个指令')
          console.log('  - 可能设备无法处理连续指令')
          console.log('  - 建议增加发送间隔')
        }

        console.log('💡 可能原因:')
        console.log('  1. 设备不在线或距离太远')
        console.log('  2. 设备地址不匹配')
        console.log('  3. 设备不支持此指令')
        console.log('  4. RF通信链路异常')
        console.log('  5. 设备正忙，无法处理新指令')
        console.log('========================')

        this.waitingForResponse = false
      }
    }, 10000)  // 10秒超时
  },

  /**
   * 开始设备发现（接收按钮）
   * 用户点击"接收"按钮时调用  主要流程：检查状态 → 延迟后开始扫描 这里是返回数据
   */
  startDiscovery() {
    // 检查发现适配器是否就绪
    if (!this.data.discoveryReady) {
      wx.showModal({
        content: '初始化未成功',
        cancelColor: 'cancelColor',
      })
      console.log('接收初始化失败')
      this.initDiscoveryAdapter()  // 重新初始化
      return
    }

    // 延迟100ms后开始扫描，确保状态稳定  然后调用startBluetoothDevicesDiscovery() 蓝牙设备监听器 接收发送的数据
    setTimeout(() => {
      this.startBluetoothDevicesDiscovery()  // 开始扫描
    }, 100);
  },
  /**
   * 初始化蓝牙设备发现功能  在此处初始化蓝牙设备发现功能 还没有查找
   * 配置设备扫描参数，但不立即开始扫描
   */
  initBluetoothDevicesDiscovery() {
    // 防止重复初始化
    if (this.data.discoveryStarted) {
      return
    }

    if (!this.data.discoveryStarted) {
      wx.startBluetoothDevicesDiscovery({  // 🎯 配置扫描参数
        allowDuplicatesKey: true,  // 允许重复上报同一设备，用于实时数据接收  实时接收
        powerLevel: "high",        // 高功率扫描，增加接收范围
        // services: ['11:22:33:44:55:66'],  // 可选：指定服务UUID过滤
        success: (res) => {
          console.log('startBluetoothDevicesDiscovery success! ', res)
          this.setData({ discoveryReady: true })  // 标记发现功能就绪
        },
        fail: (res) => {
          console.log('startBluetoothDevicesDiscovery failed! ', res)
        }
      })
    }
  },

  /**
   * 开始蓝牙设备发现
   * 设置设备发现监听器并标记扫描状态
   */
  startBluetoothDevicesDiscovery() {
    // 防止重复启动
    // 如果已经启动过蓝牙设备发现，则直接返回
    if (this.data.discoveryStarted)
      return

    // 设置设备发现监听器，用来接收其他设备发送的数据 此处接收数据
    this.onBluetoothDeviceFound()

    // 标记扫描已开始
    this.setData({ "discoveryStarted": true })
  },

  /**
   * 停止蓝牙设备发现
   * 取消监听器并更新状态
   */
  stopBluetoothDevicesDiscovery() {
    // 检查是否正在扫描
    if (!this.data.discoveryStarted)
      return

    this.setData({ "discoveryStarted": false })  // 标记扫描已停止
    wx.offBluetoothDeviceFound()                 // 取消设备发现监听器
  },
  /**
   * 设备发现事件监听器
   * 当扫描到蓝牙设备时触发，处理接收到的数据
   */
  onBluetoothDeviceFound() {
    const filterName = this.data.filterName  // 获取设备名称过滤器

    wx.onBluetoothDeviceFound((res) => {

      // 🎯 简化设备搜索打印（避免刷屏）
      if (res.devices && res.devices.length > 0) {
        // 只在发现新设备或目标设备时打印
        const targetDevices = res.devices.filter(d => d.localName === filterName)
        const targetDeviceId = '42DC0C2B-2C3B-B66D-9723-9758171C87D5'
        const deviceById = res.devices.find(d => d.deviceId === targetDeviceId)

        // 只在第一次找到目标设备时打印详细信息
        if (targetDevices.length > 0 || deviceById) {
          if (!this.targetDeviceFound) {
            this.targetDeviceFound = true
            console.log('🎯 === 找到目标设备 ===')
            const targetDevice = targetDevices[0] || deviceById
            console.log('📱 设备:', targetDevice.localName)
            console.log('🔋 信号:', targetDevice.RSSI, 'dBm')
            if (targetDevice.advertisData) {
              const data = ab2hex(targetDevice.advertisData)
              console.log('📦 数据:', data.substring(4))
            }
            console.log('========================')
          }
        }

        // 如果没有找到目标设备，只打印一次建议（避免重复）
        if (targetDevices.length === 0 && !deviceById && !this.lastSuggestionTime ||
          (Date.now() - this.lastSuggestionTime) > 10000) {  // 10秒内不重复提示
          this.lastSuggestionTime = Date.now()
          const namedDevices = res.devices.filter(d => d.localName)
          if (namedDevices.length > 0) {
            // console.log('💡 未找到目标设备，发现其他设备:')
            namedDevices.forEach((device, index) => {
              // console.log(`   ${index + 1}. "${device.localName}" (${device.RSSI}dBm)`)
            })
          }
        }
      }

      res.devices.forEach((device) => {
        // 🎯 多种方式识别目标设备
        let shouldProcess = false
        let matchReason = ''

        // 已知的目标设备ID
        const targetDeviceId = '42DC0C2B-2C3B-B66D-9723-9758171C87D5'

        // 1. 通过设备ID匹配（最准确）
        if (device.deviceId === targetDeviceId) {
          shouldProcess = true
          matchReason = '设备ID匹配'
          // console.log('🎯 ✅ 通过ID找到目标设备:', device.deviceId)
        }
        // 2. 通过设备名称匹配
        else if (device.localName && device.localName === filterName) {
          shouldProcess = true
          matchReason = '设备名称匹配'
          console.log('✅ 通过名称找到目标设备:', device.localName)
        }
        // 3. 如果没有找到TEMP，尝试处理可能的RF设备（但不自动切换）
        else if (filterName === 'TEMP' && device.localName) {
          // 检查是否是可能的RF设备（数字名称，特定长度等）
          if (device.localName.match(/^\d+$/) && device.localName.length >= 8) {
            shouldProcess = true
            matchReason = '可能的RF设备'
            // 只在第一次发现时打印，避免刷屏
            if (!this.discoveredRFDevices) {
              this.discoveredRFDevices = new Set()
            }
            if (!this.discoveredRFDevices.has(device.localName)) {
              this.discoveredRFDevices.add(device.localName)
              // console.log('🧪 发现可能的RF设备:', device.localName)
            }
          }
        }

        if (!shouldProcess) {
          return
        }

        // 只在第一次发现设备时打印详细信息
        if (!this.processedDevices) {
          this.processedDevices = new Set()
        }

        const deviceKey = `${device.deviceId}_${matchReason}`
        if (!this.processedDevices.has(deviceKey)) {
          this.processedDevices.add(deviceKey)
          // console.log('📋 匹配原因:', matchReason)
          // console.log(`🔗 发现设备: ${device.localName} (ID: ${device.deviceId.substring(0, 8)}...)`)
        }

        // 查找到对应的设备以后才进行处理 往下走，对输入的指令进行处理
        // 2. 更新设备列表（避免重复添加）
        const foundDevices = this.data.devices    // 新设备：在设备列表中没找到，添加到列表末尾
        const idx = inArray(foundDevices, 'deviceId', device.deviceId) // 检查设备是否已存在
        const data = {}
        if (idx === -1) {
          // 新设备：在设备列表中没找到，添加到列表末尾
          data[`devices[${foundDevices.length}]`] = device
        } else {
          // 已存在设备，更新信息
          data[`devices[${idx}]`] = device
        }

        // 3. 提取并处理广播数据
        let hexData = ab2hex(device.advertisData)  // 转换为十六进制字符串
        hexData = hexData.substring(4)             // 去掉前4位（BLE头部）
        device.data = hexData

        // 🎯 简化数据打印（避免刷屏）
        const currentDataKey = `${device.deviceId}_${hexData}`
        const isNewData = !this.lastPrintedData || this.lastPrintedData !== currentDataKey

        if (isNewData) {
          this.lastPrintedData = currentDataKey
          // 只打印关键信息，用于观察连续指令处理
          // console.log(`� ${device.localName}: ${hexData} (${device.RSSI}dBm)`)
        }

        // 🎯 检查是否是对发送指令的响应
        // 改进的响应检测逻辑：检查数据是否符合响应模式
        const isValidResponse = this.isValidCommandResponse(hexData, this.sentCommand)

        if (this.waitingForResponse && this.responseTimeout && isValidResponse) {
          const responseTime = ((Date.now() - this.responseStartTime) / 1000).toFixed(1)
          clearTimeout(this.responseTimeout)
          this.waitingForResponse = false

          // 🎯 连续发送响应分析
          if (!this.responseCount) this.responseCount = 0
          this.responseCount++

          // � 完整的响应结果打印
          this.printCompleteResponseResult(hexData, responseTime, device)

          // 🎯 分析连续发送的响应率
          if (this.sendCount > 1) {
            const responseRate = ((this.responseCount / this.sendCount) * 100).toFixed(1)
            // console.log('📊 响应率:', responseRate + '%')
            if (responseRate < 100) {
              console.log('⚠️ 部分指令可能未得到响应')
            }
          }

          // 🎯 分析设备响应数据变化，判断是否真正执行
          if (this.lastResponseData && this.lastResponseData !== hexData) {
            console.log('🔄 响应数据发生变化，设备可能执行了新动作')
            console.log('📊 上次响应:', this.lastResponseData)
            console.log('📊 本次响应:', hexData)
          } else if (this.lastResponseData === hexData) {
            console.log('⚠️ 响应数据相同，设备可能未执行物理动作')
            console.log('💡 可能原因: 设备忙碌、防抖机制、或状态锁定')
          }
          this.lastResponseData = hexData

          // 详细的响应分析
          this.analyzeDeviceResponse(hexData, this.sentCommand)

          console.log('========================')
        } else {
          // 🎯 非响应数据也进行完整打印
          this.printCompleteDataResult(hexData, device, '主动发送')
        }

        // 4. 生成时间戳并格式化接收数据
        const myDate = new Date();
        const time = myDate.toLocaleTimeString() + " " + myDate.getMilliseconds()
        const receiveData = { time, data: hexData }

        // 5. 更新接收数据列表（限制最大100条，防止内存溢出）
        let lastDataList = this.data.receiveDataList // 获取当前接收数据列表
        if (lastDataList.length > 100) {
          lastDataList = lastDataList.slice(0, 100)  // 保留最新的100条
        }

        // 6. 将新数据添加到列表开头（最新的在上面） 此处添加接收到的数据到列表中 显示到页面
        this.setData({ receiveDataList: [receiveData, ...lastDataList] })
      })
    })
  },

  /**
   * 完整的响应结果打印
   * 打印指令响应的完整详细信息
   */
  printCompleteResponseResult(hexData, responseTime, device) {
    console.log('')
    console.log('🎉 ========================================')
    console.log('🎉 === 指令响应完整结果 ===')
    console.log('🎉 ========================================')

    // 基本响应信息
    console.log('✅ 设备已接收并响应指令')
    console.log('⏰ 响应时间:', new Date().toLocaleString())
    console.log('⚡ 响应延迟:', responseTime, '秒')
    console.log('')

    // 设备信息
    console.log('📱 === 响应设备信息 ===')
    console.log('📱 设备名称:', device.localName || device.name || '未知设备')
    console.log('🆔 设备ID:', device.deviceId || '无ID')
    console.log('🔋 信号强度:', device.RSSI || '未知', 'dBm')
    console.log('')

    // 指令对比信息
    console.log('📋 === 指令对比信息 ===')
    console.log('📤 发送指令:', this.sentCommand)
    console.log('📥 设备响应:', hexData)
    console.log('📏 发送长度:', this.sentCommand.replace(/\s+/g, '').length / 2, '字节')
    console.log('📏 响应长度:', hexData.length / 2, '字节')
    console.log('')

    // 响应类型判断
    console.log('🔍 === 响应类型分析 ===')
    this.analyzeResponseType(hexData, this.sentCommand)
    console.log('')

    console.log('🎉 ========================================')
    console.log('🎉 === 响应结果打印完成 ===')
    console.log('🎉 ========================================')
    console.log('')
  },

  /**
   * 完整的数据结果打印（非响应数据）
   * 打印设备主动发送数据的完整信息
   */
  printCompleteDataResult(hexData, device, dataType) {
    // 避免刷屏，只在数据变化时打印
    const dataKey = `${device.deviceId}_${hexData}_${dataType}`
    if (this.lastCompleteDataKey === dataKey) {
      return
    }
    this.lastCompleteDataKey = dataKey

    // 简化打印
    console.log('📡 设备主动数据 -', device.localName || '未知', ':', hexData, '(', device.RSSI || '未知', 'dBm)')


  },

  /**
   * 分析设备响应数据
   * 简单分析响应数据的含义
   */
  analyzeDeviceResponse(responseData, originalCommand) {
    console.log('🔍 === 响应数据分析 ===')

    // 基本信息
    console.log('📊 响应数据长度:', responseData.length / 2, '字节')
    console.log('📋 原始指令长度:', originalCommand.replace(/\s+/g, '').length / 2, '字节')

    // 简单的响应模式识别
    if (responseData.length === 0) {
      console.log('❓ 空响应 - 设备可能只确认接收，无返回数据')
    } else if (responseData.startsWith('80')) {
      console.log('✅ 成功响应 - 设备确认指令执行成功')
    } else if (responseData.startsWith('FF')) {
      console.log('❌ 错误响应 - 设备报告指令执行失败')
    } else if (responseData.startsWith('81')) {
      console.log('📊 数据响应 - 设备返回查询数据')
      const dataValue = responseData.substring(2)
      console.log('📈 返回数据值:', dataValue)
    } else if (responseData === originalCommand.replace(/\s+/g, '').toLowerCase()) {
      console.log('🔄 回显响应 - 设备回显了发送的指令')
    } else {
      console.log('📡 自定义响应 - 设备返回自定义数据')
    }

    // 响应时间评估
    const responseTime = ((Date.now() - this.responseStartTime) / 1000).toFixed(1)
    if (responseTime < 1) {
      // console.log('⚡ 响应速度: 极快 (<1秒)')
    } else if (responseTime < 3) {
      // console.log('🚀 响应速度: 快速 (<3秒)')
    } else if (responseTime < 5) {
      // console.log('🐌 响应速度: 一般 (<5秒)')
    } else {
      // console.log('🐢 响应速度: 较慢 (>5秒)')
    }

    console.log('===================')
  },

  /**
   * 验证是否是有效的指令响应
   * 根据指令响应模式判断收到的数据是否是真正的响应
   */
  isValidCommandResponse(responseData, sentCommand) {
    if (!this.waitingForResponse || !sentCommand || !responseData) {
      return false
    }

    const cleanResponse = responseData.replace(/\s+/g, '').toLowerCase()
    const cleanCommand = sentCommand.replace(/\s+/g, '').toLowerCase()

    // 检查是否是81开头的控制指令响应
    if (cleanResponse.startsWith('81') && cleanCommand.startsWith('81')) {
      // 检查指令格式：81 00 02 XX YY ZZ 00 FA
      // 响应格式应该是：81 00 02 (XX+1) YY (ZZ+1) 00 FA

      if (cleanResponse.length === cleanCommand.length && cleanResponse.length >= 16) {
        // 提取关键字节进行比较
        const cmdByte4 = cleanCommand.substring(6, 8)   // 第4字节
        const cmdByte6 = cleanCommand.substring(10, 12) // 第6字节

        const respByte4 = cleanResponse.substring(6, 8)  // 第4字节
        const respByte6 = cleanResponse.substring(10, 12) // 第6字节

        // 检查响应是否符合模式：第4字节+1，第6字节+1
        const expectedByte4 = (parseInt(cmdByte4, 16) + 1).toString(16).padStart(2, '0')
        const expectedByte6 = (parseInt(cmdByte6, 16) + 1).toString(16).padStart(2, '0')

        if (respByte4 === expectedByte4 && respByte6 === expectedByte6) {
          console.log('✅ 检测到有效的指令响应模式')
          console.log('📋 指令第4字节:', cmdByte4, '→ 响应:', respByte4, '(+1)')
          console.log('📋 指令第6字节:', cmdByte6, '→ 响应:', respByte6, '(+1)')
          return true
        }
      }
    }

    // 检查其他响应模式
    if (cleanResponse.startsWith('80') || cleanResponse.startsWith('ff')) {
      return true // 确认响应或错误响应
    }

    return false
  },

  /**
   * 十六进制数据简单分析
   * 简单显示数据内容和类型
   */
  printHexDataAnalysis(dataName, hexData) {
    if (!hexData || hexData.length === 0) {
      console.log(`${dataName}: 空数据`)
      return
    }

    const cleanData = hexData.replace(/\s+/g, '').toUpperCase()
    console.log(`${dataName}: ${cleanData}`)

    // 尝试识别常见模式
    if (cleanData.startsWith('81')) {
      console.log(`${dataName} 类型: 控制指令`)
    } else if (cleanData.startsWith('80')) {
      console.log(`${dataName} 类型: 确认响应`)
    } else if (cleanData.startsWith('FF')) {
      console.log(`${dataName} 类型: 错误响应`)
    }
  },

  /**
   * 响应类型分析
   * 详细分析响应数据的类型和含义
   */
  analyzeResponseType(responseData, originalCommand) {
    const cleanResponse = responseData.replace(/\s+/g, '').toLowerCase()
    const cleanCommand = originalCommand.replace(/\s+/g, '').toLowerCase()

    if (responseData.length === 0) {
      console.log('❓ 空响应 - 设备可能只确认接收，无返回数据')
    } else if (cleanResponse === cleanCommand) {
      console.log('🔄 回显响应 - 设备完全回显了发送的指令')
    } else if (responseData.startsWith('80')) {
      console.log('✅ 成功响应 - 设备确认指令执行成功')
    } else if (responseData.startsWith('FF')) {
      console.log('❌ 错误响应 - 设备报告指令执行失败')
    } else if (responseData.startsWith('81')) {
      console.log('📊 数据响应 - 设备返回查询数据')
      const dataValue = responseData.substring(2)
      console.log('📈 返回数据值:', dataValue)
    } else {
      console.log('📡 自定义响应 - 设备返回自定义数据')

      // 尝试找出相似性
      let similarity = 0
      const minLength = Math.min(cleanResponse.length, cleanCommand.length)
      for (let i = 0; i < minLength; i += 2) {
        if (cleanResponse.substr(i, 2) === cleanCommand.substr(i, 2)) {
          similarity++
        }
      }
      const similarityPercent = ((similarity / (minLength / 2)) * 100).toFixed(1)
      // console.log('🔍 与原指令相似度:', similarityPercent + '%')
    }
  },

  /**
   * 通信质量评估
   * 根据响应时间和信号强度评估通信质量
   */
  evaluateCommunicationQuality(responseTime, rssi) {
    // 响应时间评估
    const responseTimeFloat = parseFloat(responseTime)
    let timeScore = 0
    if (responseTimeFloat < 0.5) {
      console.log('⚡ 响应速度: 极快 (<0.5秒) - 优秀')
      timeScore = 5
    } else if (responseTimeFloat < 1) {
      console.log('🚀 响应速度: 很快 (<1秒) - 良好')
      timeScore = 4
    } else if (responseTimeFloat < 2) {
      console.log('✅ 响应速度: 快速 (<2秒) - 正常')
      timeScore = 3
    } else if (responseTimeFloat < 5) {
      console.log('🐌 响应速度: 一般 (<5秒) - 可接受')
      timeScore = 2
    } else {
      console.log('🐢 响应速度: 较慢 (>5秒) - 需优化')
      timeScore = 1
    }

    // 信号强度评估
    let signalScore = 0
    if (rssi && rssi > -50) {
      console.log('📶 信号强度: 极强 (>-50dBm) - 优秀')
      signalScore = 5
    } else if (rssi && rssi > -60) {
      console.log('📶 信号强度: 很强 (>-60dBm) - 良好')
      signalScore = 4
    } else if (rssi && rssi > -70) {
      console.log('📶 信号强度: 强 (>-70dBm) - 正常')
      signalScore = 3
    } else if (rssi && rssi > -80) {
      console.log('📶 信号强度: 中等 (>-80dBm) - 可接受')
      signalScore = 2
    } else if (rssi) {
      console.log('📶 信号强度: 弱 (<-80dBm) - 需优化')
      signalScore = 1
    } else {
      console.log('📶 信号强度: 未知')
      signalScore = 3
    }

    // 综合评分
    const totalScore = (timeScore + signalScore) / 2
    if (totalScore >= 4.5) {
      console.log('🏆 通信质量: 优秀 - 系统运行完美')
    } else if (totalScore >= 3.5) {
      console.log('✅ 通信质量: 良好 - 系统运行正常')
    } else if (totalScore >= 2.5) {
      console.log('⚠️ 通信质量: 一般 - 建议优化环境')
    } else {
      console.log('❌ 通信质量: 较差 - 需要检查设备和环境')
    }
  },

  /**
   * 关闭蓝牙适配器
   * 完全关闭蓝牙功能，释放所有资源
   */
  closeBluetoothAdapter() {
    wx.closeBluetoothAdapter()
    this.setData({ "discoveryStarted": false })
  },

  /**
   * 创建BLE外设服务器
   * 用于BLE广播功能，创建可被其他设备发现的服务器
   */
  createBLEPeripheralServer() {
    wx.createBLEPeripheralServer().then(res => {
      console.log('createBLEPeripheralServer', res)
      this.data.servers.push(res.server)    // 添加到服务器列表
      this.server = res.server               // 保存当前服务器实例
      // 标记广播功能就绪，保存服务器ID this.server.serverId 蓝牙特征对应服务的 UUID
      this.setData({ advertiseReady: true, serverId: this.server.serverId })
    })
  },

  /**
   * 关闭BLE服务器
   * 停止广播并释放服务器资源
   */
  closeServer() {
    this.server.close()
  },

  /**
   * 开始BLE广播  发送指令
   * 根据平台类型选择不同的数据封装方式进行广播
   * @param {Array} actPayload - 经过处理的BLE数据包
   */
  startAdvertising(actPayload) {
    // 1. 检测系统平台
    const isIos = this.system.indexOf('iOS') >= 0
    const isIos13 = isIos && this.system.indexOf('13.') >= 0  // iOS 13有特殊处理

    console.log('actPayload', actPayload)

    // 2. 根据平台生成Service UUIDs（主要用于iOS）
    const uuids = getServiceUUIDs(actPayload, isIos13)

    // 3. 开始BLE广播，根据平台使用不同的数据封装方式 此处封装了BLE广播数据包，并将其发送出去。 在这里发送指令了
    // this.server.startAdvertising此方法来自一开始wx.createBLEPeripheralServer() 创建服务器实例 BLEPeripheralServer实例上面有这个方法 用来开始广播本地创建的外围设备。
    this.server.startAdvertising({
      advertiseTimeout: 0,
      advertiseRequest: {
        connectable: true,                    // 允许连接
        deviceName: isIos ? '11' : '',        // iOS设备名为'11'，Android为空

        // iOS平台：数据编码到Service UUIDs中
        serviceUuids: isIos ? uuids : [],

        // Android平台：数据放在Manufacturer Data中
        manufacturerData: isIos ? [] : [{
          manufacturerId: '0x00C7',           // 制造商ID
          manufacturerSpecificData: actPayload, // 实际数据
        }]
      },
      powerLevel: 'high'  // 高功率广播，增加传输距离
    }).then(res => {
      // 广播成功
      console.log('startAdvertising', res)    // 这会导致界面按钮文字变化： "发送广播包" → "停止发送"
      this.setData({ advertiseStart: true })  // 更新广播状态 广播成功后，只是更新了界面状态 只是确认 没有立刻返回数据 点击接收才返回数据
    }, res => {
      // 广播失败
      this.setData({ advertiseStart: false })
      console.log("startAdvertising fail: ", res)
    })
  },

  /**
   * 停止BLE广播
   * 停止数据发送并更新状态
   */
  stopAdvertising() {
    if (this.server) {
      console.log('📤 === 停止发送操作 ===')
      console.log('⏰ 停止时间:', new Date().toLocaleString())

      // 🎯 停止连续发送模式
      if (this.continuousSendTimer) {
        clearInterval(this.continuousSendTimer)
        this.continuousSendTimer = null
        console.log('⏹️ 停止连续发送模式')

        // 对于控制指令，发送停止指令
        this.sendStopCommand()
        return  // sendStopCommand会处理后续的停止广播
      }

      // 🎯 停止持续前进模式
      if (this.continuousForwardTimer) {
        this.stopContinuousForward()
        return
      }

      console.log('📋 当前操作: 停止BLE广播')
      console.log('❌ 注意: 未向RF设备发送停止指令')
      console.log('💡 RF设备可能继续执行之前的指令')

      this.server.stopAdvertising()           // 停止广播
      this.setData({ advertiseStart: false }) // 更新状态

      console.log('✅ BLE广播已停止')
      console.log('📱 界面状态已更新')
      console.log('========================')
    }
  },

  /**
   * 发送停止指令（可选功能）
   * 如果需要通知RF设备停止，可以调用此函数
   */
  sendStopCommandToDevice() {
    console.log('📤 === 发送停止指令给RF设备 ===')

    // 🎯 定义停止指令（请根据您的RF协议修改）
    const stopCommand = 'FF FF FF FF'  // 示例停止指令

    console.log('📋 停止指令:', stopCommand)
    console.log('⏰ 发送时间:', new Date().toLocaleString())

    try {
      const isIos = this.system.indexOf('iOS') >= 0
      const stopPayload = generateData(stopCommand, isIos)

      if (stopPayload && this.server) {
        this.server.startAdvertising(stopPayload).then(res => {
          console.log('✅ 停止指令发送成功')
          console.log('💡 RF设备应该会停止当前操作')
        }).catch(err => {
          console.log('❌ 停止指令发送失败:', err)
        })
      }
    } catch (error) {
      console.log('❌ 发送停止指令时出错:', error)
    }

    console.log('================================')
  },

  /**
   * 连续发送模式（适用于控制指令）
   * 按住按钮时持续发送指令，松开时停止
   */
  startContinuousSend() {
    let payload = this.data.payload

    // 验证输入
    if (!payload || payload.trim() === '') {
      wx.showToast({
        icon: 'none',
        title: 'payload不可为空',
      })
      return
    }

    console.log('🔄 === 开始连续发送模式 ===')
    console.log('📋 控制指令:', payload)
    console.log('⏰ 开始时间:', new Date().toLocaleString())
    console.log('🔁 发送间隔: 200ms')

    const isIos = this.system.indexOf('iOS') >= 0
    const actPayload = generateData(payload, isIos);

    if (!actPayload) {
      console.log('❌ 指令生成失败')
      return
    }

    // 立即发送第一次
    this.startAdvertising(actPayload)
    this.setData({ advertiseStart: true })

    // 设置连续发送定时器
    this.continuousSendCount = 0
    this.continuousSendTimer = setInterval(() => {
      this.continuousSendCount++
      // 只每5次打印一次，避免刷屏
      if (this.continuousSendCount % 5 === 0) {
        console.log(`🔁 连续发送 ${this.continuousSendCount} 次: ${payload}`)
      }
      this.startAdvertising(actPayload)
    }, 200)  // 每200ms发送一次

    console.log('✅ 连续发送模式已启动')
    console.log('💡 点击停止按钮来停止连续发送')
    console.log('===============================')
  },

  /**
   * 停止连续发送
   */
  stopContinuousSend() {
    if (this.continuousSendTimer) {
      clearInterval(this.continuousSendTimer)
      this.continuousSendTimer = null
      console.log('⏹️ 连续发送已停止')

      // 发送停止指令
      this.sendStopCommand()
    }
  },

  /**
   * 发送停止指令给设备
   * 用于控制类指令，通知设备停止当前动作
   */
  sendStopCommand() {
    console.log('📤 === 发送停止指令 ===')

    // 🎯 定义停止指令（请根据您的协议修改）
    const stopCommand = '00 00 00 00'  // 示例：全零表示停止

    console.log('📋 停止指令:', stopCommand)
    console.log('⏰ 发送时间:', new Date().toLocaleString())

    try {
      const isIos = this.system.indexOf('iOS') >= 0
      const stopPayload = generateData(stopCommand, isIos)

      if (stopPayload && this.server) {
        this.startAdvertising(stopPayload).then(res => {
          console.log('✅ 停止指令发送成功')
          console.log('🛑 设备应该停止当前动作')

          // 发送停止指令后，延迟停止广播
          setTimeout(() => {
            this.server.stopAdvertising()
            this.setData({ advertiseStart: false })
            console.log('📱 BLE广播已停止')
          }, 300)  // 给设备300ms时间接收停止指令

        }).catch(err => {
          console.log('❌ 停止指令发送失败:', err)
        })
      }
    } catch (error) {
      console.log('❌ 发送停止指令时出错:', error)
    }

    console.log('========================')
  },

  /**
   * 重置日志缓存（清除重复打印的缓存）
   * 用于重新开始观察设备发现过程
   */
  resetLogCache() {
    this.targetDeviceFound = false
    this.processedDevices = new Set()
    this.discoveredRFDevices = new Set()
    this.lastSuggestionTime = 0
    this.lastPrintedData = null

    console.log('🔄 === 日志缓存已重置 ===')
    console.log('💡 现在可以重新观察设备发现过程')
    console.log('========================')
  },

  /**
   * 重置连续发送计数器
   * 用于重新开始测试连续发送
   */
  resetSendCounter() {
    this.sendCount = 0
    this.responseCount = 0
    this.lastSendTime = null
    this.previousSendTime = null
    this.lastResponseData = null

    console.log('🔄 === 连续发送计数器已重置 ===')
    console.log('💡 现在可以重新测试连续发送')
    console.log('========================')
  },

  /**
   * 测试间隔发送
   * 用指定间隔发送多次相同指令，观察设备执行情况
   */
  testIntervalSend(intervalMs = 3000, count = 3) {
    if (!this.data.payload) {
      console.log('❌ 请先设置payload')
      return
    }

    console.log('🧪 === 开始间隔发送测试 ===')
    console.log('📋 测试指令:', this.data.payload)
    console.log('⏱️ 发送间隔:', intervalMs, 'ms')
    console.log('🔢 发送次数:', count)
    console.log('💡 观察每次发送后设备是否都有物理动作')
    console.log('===============================')

    this.resetSendCounter()

    let currentCount = 0
    const sendInterval = setInterval(() => {
      currentCount++
      console.log(`\n🚀 第 ${currentCount} 次间隔发送`)
      this.startSendAndDiscovery()

      if (currentCount >= count) {
        clearInterval(sendInterval)
        console.log('\n✅ 间隔发送测试完成')
        console.log('💡 请观察设备是否每次都有物理动作')
      }
    }, intervalMs)
  },

  /**
   * 持续前进功能
   * 专门用于实现持续前进，自动处理间隔
   */
  startContinuousForward() {
    // 设置前进指令
    this.setData({ payload: '81000210011300FA' })

    console.log('🚗 === 开始持续前进 ===')
    console.log('📋 前进指令: 81000210011300FA')
    console.log('⏱️ 发送间隔: 800ms (适合前进节奏)')
    console.log('🛑 点击"停止发送"或调用stopContinuousForward()停止')
    console.log('============================')

    this.resetSendCounter()

    // 立即发送第一次
    this.startSendAndDiscovery()

    // 设置持续前进定时器（800ms间隔，适合前进动作）
    this.continuousForwardTimer = setInterval(() => {
      console.log('🚗 持续前进中...')
      this.startSendAndDiscovery()
    }, 800)  // 800ms间隔，给设备足够时间完成每段前进

    // 更新状态
    this.setData({ advertiseStart: true })
  },

  /**
   * 停止持续前进
   */
  stopContinuousForward() {
    if (this.continuousForwardTimer) {
      clearInterval(this.continuousForwardTimer)
      this.continuousForwardTimer = null

      console.log('🛑 === 停止持续前进 ===')
      console.log('📤 发送刹车指令')

      // 发送刹车指令
      this.setData({ payload: '81000210031500FA' })  // 刹车指令
      this.startSendAndDiscovery()

      setTimeout(() => {
        this.setData({ advertiseStart: false })
        console.log('✅ 持续前进已停止')
      }, 1000)
    }
  },
})

/**
 * 文件总结：
 * 这个文件实现了一个RF通信测试工具的核心功能：
 *
 * 主要功能：
 * 1. 发送功能：将用户输入的payload转换为BLE广播包发送给RF设备
 * 2. 接收功能：扫描并接收RF设备发送的BLE广播数据
 * 3. 配置管理：跳转到配置页面设置RF地址、信道等参数
 * 4. 数据管理：显示、复制、清空接收到的数据
 *
 * 技术特点：
 * - 支持iOS和Android平台的不同BLE封装方式
 * - 实现XN297L RF协议到BLE协议的转换
 * - 提供实时数据接收和显示功能
 * - 包含完整的错误处理和状态管理
 *
 * 数据流程：
 * 发送：用户输入 → 数据验证 → BLE转换 → 平台适配 → BLE广播 → RF设备接收
 * 接收：RF设备发送 → BLE广播 → 设备过滤 → 数据提取 → 界面显示
 */
