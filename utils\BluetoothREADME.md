# 蓝牙工具封装使用说明

本项目提供了两个蓝牙工具类，用于简化微信小程序中的蓝牙开发：

1. **BluetoothManager** - 完整功能的蓝牙管理类
2. **SimpleBluetooth** - 简化版蓝牙工具类

## 1. SimpleBluetooth（推荐新手使用）

### 基本使用

```javascript
import SimpleBluetooth from '../utils/SimpleBluetooth'

Page({
  async onLoad() {
    try {
      // 初始化蓝牙
      await SimpleBluetooth.init()
      console.log('蓝牙初始化成功')
    } catch (error) {
      console.error('蓝牙初始化失败:', error)
    }
  },

  // 开始接收数据
  async startReceive() {
    try {
      await SimpleBluetooth.startReceive('TEMP', (data) => {
        console.log('接收到数据:', data)
        // data 格式: { time, data, device }
      })
    } catch (error) {
      console.error('开始接收失败:', error)
    }
  },

  // 发送广播数据
  async sendData() {
    try {
      await SimpleBluetooth.send('1234567890abcdef')
      console.log('发送成功')
    } catch (error) {
      console.error('发送失败:', error)
    }
  },

  // 停止接收
  stopReceive() {
    SimpleBluetooth.stopReceive()
  },

  // 停止发送
  stopSend() {
    SimpleBluetooth.stopSend()
  },

  onUnload() {
    // 页面卸载时关闭蓝牙
    SimpleBluetooth.close()
  }
})
```

### API 说明

- `init(options)` - 初始化蓝牙
- `startReceive(filterName, onDataReceived)` - 开始接收数据
- `send(payload)` - 发送广播数据
- `stopReceive()` - 停止接收
- `stopSend()` - 停止发送
- `close()` - 关闭蓝牙

## 2. BluetoothManager（高级用户使用）

### 基本使用

```javascript
import BluetoothManager from '../utils/BluetoothManager'

Page({
  data: {
    receiveDataList: [],
    devices: []
  },

  onLoad() {
    this.initBluetooth()
  },

  initBluetooth() {
    // 创建蓝牙管理器
    this.bluetoothManager = new BluetoothManager({
      filterName: 'TEMP',
      maxReceiveDataCount: 100,
      powerLevel: 'high'
    })

    // 设置事件监听
    this.bluetoothManager.on('onDataReceived', (data) => {
      this.setData({
        receiveDataList: this.bluetoothManager.getReceiveDataList()
      })
    })

    this.bluetoothManager.on('onDeviceFound', (device) => {
      this.setData({
        devices: this.bluetoothManager.getDevices()
      })
    })

    this.bluetoothManager.on('onError', (error) => {
      wx.showToast({
        title: error.message || '操作失败',
        icon: 'none'
      })
    })

    // 初始化适配器
    this.initAdapters()
  },

  async initAdapters() {
    try {
      await Promise.all([
        this.bluetoothManager.initDiscoveryAdapter(),
        this.bluetoothManager.initAdvertiseAdapter()
      ])
    } catch (error) {
      console.error('初始化失败:', error)
    }
  },

  async startScan() {
    try {
      await this.bluetoothManager.startBluetoothDevicesDiscovery()
    } catch (error) {
      console.error('开始扫描失败:', error)
    }
  },

  async startBroadcast() {
    try {
      await this.bluetoothManager.startAdvertising('1234567890abcdef')
    } catch (error) {
      console.error('开始广播失败:', error)
    }
  },

  onUnload() {
    if (this.bluetoothManager) {
      this.bluetoothManager.destroy()
    }
  }
})
```

### 配置选项

```javascript
const bluetoothManager = new BluetoothManager({
  filterName: 'TEMP',              // 设备过滤名称
  maxReceiveDataCount: 100,        // 最大接收数据条数
  powerLevel: 'high',              // 功率级别: 'high', 'medium', 'low'
  allowDuplicatesKey: true         // 是否允许重复设备
})
```

### 事件监听

- `onDeviceFound` - 发现设备
- `onDataReceived` - 接收到数据
- `onAdvertiseStateChange` - 广播状态变化
- `onDiscoveryStateChange` - 扫描状态变化
- `onError` - 错误事件

### 主要方法

- `initDiscoveryAdapter()` - 初始化扫描适配器
- `initAdvertiseAdapter()` - 初始化广播适配器
- `startBluetoothDevicesDiscovery()` - 开始设备扫描
- `stopBluetoothDevicesDiscovery()` - 停止设备扫描
- `startAdvertising(payload)` - 开始广播
- `stopAdvertising()` - 停止广播
- `getReceiveDataList()` - 获取接收数据列表
- `getDevices()` - 获取设备列表
- `clearReceiveDataList()` - 清空接收数据
- `destroy()` - 销毁实例

## 3. 在现有页面中集成

### 替换原有代码

如果你有现有的蓝牙页面，可以这样替换：

**原来的代码：**
```javascript
// 原来直接调用微信API
wx.openBluetoothAdapter({...})
wx.startBluetoothDevicesDiscovery({...})
```

**替换为：**
```javascript
// 使用封装的工具
import SimpleBluetooth from '../utils/SimpleBluetooth'

// 初始化
await SimpleBluetooth.init()

// 开始接收
await SimpleBluetooth.startReceive('TEMP', (data) => {
  // 处理接收到的数据
})
```

## 4. 注意事项

1. **权限配置**：确保在 `app.json` 中配置了蓝牙权限
2. **生命周期管理**：在页面 `onUnload` 时记得调用 `close()` 或 `destroy()`
3. **错误处理**：建议使用 try-catch 包装异步调用
4. **数据格式**：payload 需要是十六进制字符串格式

## 5. 依赖文件

使用这些工具需要确保以下文件存在：
- `utils/util.js` - 工具函数
- `utils/BLEUtil.js` - 蓝牙数据处理
- `utils/whitening.js` - 数据白化算法
- `utils/crc16.js` - CRC校验

## 6. 示例项目结构

```
your-project/
├── pages/
│   └── bluetooth/
│       ├── bluetooth.js
│       ├── bluetooth.wxml
│       └── bluetooth.wxss
└── utils/
    ├── BluetoothManager.js
    ├── SimpleBluetooth.js
    ├── BLEUtil.js
    ├── util.js
    ├── whitening.js
    └── crc16.js
```
