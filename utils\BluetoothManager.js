/**
 * 蓝牙管理工具类
 * 封装微信小程序蓝牙相关功能，包括设备发现、广播、数据传输等
 */

import { ab2hex, inArray } from './util'
import { generateData, getServiceUUIDs } from './BLEUtil'

class BluetoothManager {
  constructor(options = {}) {
    // 默认配置
    this.config = {
      filterName: options.filterName || 'TEMP',
      maxReceiveDataCount: options.maxReceiveDataCount || 100,
      powerLevel: options.powerLevel || 'high',
      allowDuplicatesKey: options.allowDuplicatesKey !== false,
      ...options
    }

    // 状态管理
    this.state = {
      discoveryStarted: false,
      advertiseStart: false,
      discoveryReady: false,
      advertiseReady: false,
      system: 'android'
    }

    // 数据存储
    this.devices = []
    this.servers = []
    this.server = null
    this.receiveDataList = []

    // 事件回调
    this.callbacks = {
      onDeviceFound: null,
      onDataReceived: null,
      onAdvertiseStateChange: null,
      onDiscoveryStateChange: null,
      onError: null
    }

    this.initSystemInfo()
  }

  /**
   * 初始化系统信息
   */
  initSystemInfo() {
    try {
      const { system } = wx.getSystemInfoSync()
      this.state.system = system
      console.log('BluetoothManager system:', system)
    } catch (error) {
      console.error('获取系统信息失败:', error)
    }
  }

  /**
   * 设置事件回调
   * @param {string} eventName - 事件名称
   * @param {function} callback - 回调函数
   */
  on(eventName, callback) {
    if (this.callbacks.hasOwnProperty(eventName)) {
      this.callbacks[eventName] = callback
    } else {
      console.warn(`未知的事件名称: ${eventName}`)
    }
  }

  /**
   * 触发事件回调
   * @param {string} eventName - 事件名称
   * @param {*} data - 事件数据
   */
  emit(eventName, data) {
    if (this.callbacks[eventName] && typeof this.callbacks[eventName] === 'function') {
      this.callbacks[eventName](data)
    }
  }

  /**
   * 初始化蓝牙发现适配器
   */
  initDiscoveryAdapter() {
    return new Promise((resolve, reject) => {
      wx.openBluetoothAdapter({
        success: (res) => {
          console.log('initDiscoveryAdapter success', res)
          this.initBluetoothDevicesDiscovery()
            .then(() => resolve(res))
            .catch(reject)
        },
        fail: (res) => {
          console.log("initDiscoveryAdapter ble unavailable!", res)
          this.emit('onError', { type: 'discovery_init_failed', error: res })
          reject(res)
        }
      })
    })
  }

  /**
   * 初始化蓝牙广播适配器
   */
  initAdvertiseAdapter() {
    return new Promise((resolve, reject) => {
      wx.openBluetoothAdapter({
        mode: 'peripheral',
        success: (res) => {
          console.log('initAdvertiseAdapter success', res)
          this.createBLEPeripheralServer()
            .then(() => resolve(res))
            .catch(reject)
        },
        fail: (res) => {
          console.log("initAdvertiseAdapter ble unavailable!", res)
          this.emit('onError', { type: 'advertise_init_failed', error: res })
          reject(res)
        }
      })
    })
  }

  /**
   * 初始化蓝牙设备发现
   */
  initBluetoothDevicesDiscovery() {
    return new Promise((resolve, reject) => {
      if (this.state.discoveryStarted) {
        resolve()
        return
      }

      wx.startBluetoothDevicesDiscovery({
        allowDuplicatesKey: this.config.allowDuplicatesKey,
        powerLevel: this.config.powerLevel,
        success: (res) => {
          console.log('startBluetoothDevicesDiscovery success!', res)
          this.state.discoveryReady = true
          resolve(res)
        },
        fail: (res) => {
          console.log('startBluetoothDevicesDiscovery failed!', res)
          this.emit('onError', { type: 'discovery_start_failed', error: res })
          reject(res)
        }
      })
    })
  }

  /**
   * 开始蓝牙设备发现
   */
  startBluetoothDevicesDiscovery() {
    if (this.state.discoveryStarted) {
      return Promise.resolve()
    }

    return new Promise((resolve, reject) => {
      if (!this.state.discoveryReady) {
        this.emit('onError', { type: 'discovery_not_ready', message: '蓝牙发现未初始化' })
        reject(new Error('蓝牙发现未初始化'))
        return
      }

      this.onBluetoothDeviceFound()
      this.state.discoveryStarted = true
      this.emit('onDiscoveryStateChange', { started: true })
      resolve()
    })
  }

  /**
   * 停止蓝牙设备发现
   */
  stopBluetoothDevicesDiscovery() {
    if (!this.state.discoveryStarted) {
      return
    }

    this.state.discoveryStarted = false
    wx.offBluetoothDeviceFound()
    this.emit('onDiscoveryStateChange', { started: false })
  }

  /**
   * 监听蓝牙设备发现
   */
  onBluetoothDeviceFound() {
    wx.onBluetoothDeviceFound((res) => {
      res.devices.forEach(device => {
        if (!device.localName || device.localName !== this.config.filterName) {
          return
        }

        // 处理设备数据
        const foundDevices = this.devices
        const idx = inArray(foundDevices, 'deviceId', device.deviceId)
        
        if (idx === -1) {
          this.devices.push(device)
        } else {
          this.devices[idx] = device
        }

        // 处理广播数据
        let hexData = ab2hex(device.advertisData)
        hexData = hexData.substring(4)
        device.data = hexData

        // 添加到接收数据列表
        const myDate = new Date()
        const time = myDate.toLocaleTimeString() + " " + myDate.getMilliseconds()
        const receiveData = { time, data: hexData, device }

        // 限制数据列表长度
        if (this.receiveDataList.length >= this.config.maxReceiveDataCount) {
          this.receiveDataList = this.receiveDataList.slice(0, this.config.maxReceiveDataCount - 1)
        }
        this.receiveDataList.unshift(receiveData)

        // 触发回调
        this.emit('onDeviceFound', device)
        this.emit('onDataReceived', receiveData)
      })
    })
  }

  /**
   * 创建BLE外围设备服务器
   */
  createBLEPeripheralServer() {
    return new Promise((resolve, reject) => {
      wx.createBLEPeripheralServer()
        .then(res => {
          console.log('createBLEPeripheralServer', res)
          this.servers.push(res.server)
          this.server = res.server
          this.state.advertiseReady = true
          resolve(res)
        })
        .catch(error => {
          this.emit('onError', { type: 'server_create_failed', error })
          reject(error)
        })
    })
  }

  /**
   * 开始广播
   * @param {string} payload - 要广播的数据
   */
  startAdvertising(payload) {
    return new Promise((resolve, reject) => {
      if (!this.state.advertiseReady) {
        const error = new Error('广播服务未初始化')
        this.emit('onError', { type: 'advertise_not_ready', error })
        reject(error)
        return
      }

      if (!payload || payload.length === 0) {
        const error = new Error('payload不能为空')
        this.emit('onError', { type: 'invalid_payload', error })
        reject(error)
        return
      }

      // 处理payload
      const cleanPayload = payload.replace(/\s+/g, '')
      const isIos = this.state.system.indexOf('iOS') >= 0
      const isIos13 = isIos && this.state.system.indexOf('13.') >= 0
      
      const actPayload = generateData(cleanPayload, isIos)
      if (!actPayload) {
        const error = new Error('生成广播数据失败')
        this.emit('onError', { type: 'generate_data_failed', error })
        reject(error)
        return
      }

      console.log('actPayload', actPayload)
      const uuids = getServiceUUIDs(actPayload, isIos13)

      this.server.startAdvertising({
        advertiseRequest: {
          connectable: true,
          deviceName: isIos ? '11' : '',
          serviceUuids: isIos ? uuids : [],
          manufacturerData: isIos ? [] : [{
            manufacturerId: '0x00C7',
            manufacturerSpecificData: actPayload,
          }]
        },
        powerLevel: this.config.powerLevel
      }).then(res => {
        console.log('startAdvertising success', res)
        this.state.advertiseStart = true
        this.emit('onAdvertiseStateChange', { started: true })
        resolve(res)
      }).catch(error => {
        console.log("startAdvertising fail:", error)
        this.state.advertiseStart = false
        this.emit('onAdvertiseStateChange', { started: false })
        this.emit('onError', { type: 'advertise_start_failed', error })
        reject(error)
      })
    })
  }

  /**
   * 停止广播
   */
  stopAdvertising() {
    return new Promise((resolve) => {
      if (this.server && this.state.advertiseStart) {
        this.server.stopAdvertising()
        this.state.advertiseStart = false
        this.emit('onAdvertiseStateChange', { started: false })
      }
      resolve()
    })
  }

  /**
   * 关闭蓝牙适配器
   */
  closeBluetoothAdapter() {
    wx.closeBluetoothAdapter()
    this.state.discoveryStarted = false
    this.emit('onDiscoveryStateChange', { started: false })
  }

  /**
   * 关闭服务器
   */
  closeServer() {
    if (this.server) {
      this.server.close()
      this.server = null
    }
  }

  /**
   * 清理资源
   */
  destroy() {
    this.stopBluetoothDevicesDiscovery()
    this.stopAdvertising()
    this.servers.forEach(server => {
      server.close()
    })
    this.closeBluetoothAdapter()
    
    // 清空数据
    this.devices = []
    this.servers = []
    this.receiveDataList = []
    
    // 清空回调
    Object.keys(this.callbacks).forEach(key => {
      this.callbacks[key] = null
    })
  }

  /**
   * 获取当前状态
   */
  getState() {
    return { ...this.state }
  }

  /**
   * 获取接收到的数据列表
   */
  getReceiveDataList() {
    return [...this.receiveDataList]
  }

  /**
   * 获取发现的设备列表
   */
  getDevices() {
    return [...this.devices]
  }

  /**
   * 清空接收数据列表
   */
  clearReceiveDataList() {
    this.receiveDataList = []
  }

  /**
   * 设置过滤名称
   * @param {string} filterName - 设备过滤名称
   */
  setFilterName(filterName) {
    this.config.filterName = filterName
  }
}

export default BluetoothManager
